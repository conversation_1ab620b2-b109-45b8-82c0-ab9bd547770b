import 'package:ambulancia_app/models/motive.model.dart';
import 'package:ambulancia_app/shared/api/attendance/motive.api.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

part 'motive_state.dart';

class MotiveCubit extends Cubit<MotiveState> {
  MotiveCubit() : super(MotiveInitial());

  List<MotiveModel>? _motives;
  List<MotiveModel>? getMotives() => _motives;

  Future<void> getAttendanceMotives() async {
    try {
      emit(LoadingMotivesState());

      _motives = await Locator.instance<MotiveApi>().getMotivesCancel();

      emit(LoadedMotivesState(motives: _motives));
    } catch (ex) {
      emit(ErrorLoadMotivesState('$ex'));
    }
  }
}
