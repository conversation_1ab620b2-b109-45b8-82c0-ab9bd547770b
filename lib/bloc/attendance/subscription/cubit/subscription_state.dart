part of 'subscription_cubit.dart';

abstract class SubscriptionState {
  const SubscriptionState();
}

class SubscriptionInitial extends SubscriptionState {}

class LoadingVerifySubscriptionState extends SubscriptionState {}

class SuccessSendVerifySubscriptionState extends SubscriptionState {
  final String attachments;
  final int? codStatus;
  SuccessSendVerifySubscriptionState(this.attachments, {this.codStatus});

  bool? get signed {
    try {
      final List<dynamic> data = jsonDecode(attachments);
      return data.any((attachment) =>
        attachment['attachmentName']?.toString().startsWith('sign_') == true);
    } catch (e) {
      return false;
    }
  }
}

class ErrorVerifySubscriptionState extends SubscriptionState {
  final String message;
  ErrorVerifySubscriptionState(this.message);
}
