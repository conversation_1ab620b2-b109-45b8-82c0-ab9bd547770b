import 'package:ambulancia_app/shared/api/graphql.api.dart';
import 'package:ambulancia_app/shared/api/signature.api.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:bloc/bloc.dart';

part 'subscription_state.dart';

class SubscriptionCubit extends Cubit<SubscriptionState> {
  SubscriptionCubit() : super(SubscriptionInitial());

  Future<void> verifySubscription(int? numAtendimento, int? codStatus) async {
    try {
      emit(LoadingVerifySubscriptionState());
      final response = await Locator.instance.get<GraphQlApi>()
          .verifyExistenceSignature(numAtendimento, codStatus);
      emit(SuccessSendVerifySubscriptionState(response, codStatus: codStatus));
    } catch (e) {
      emit(ErrorVerifySubscriptionState('$e'));
    }
  }
}
