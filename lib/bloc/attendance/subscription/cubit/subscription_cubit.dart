import 'dart:convert';
import 'package:ambulancia_app/shared/api/graphql.api.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:bloc/bloc.dart';

part 'subscription_state.dart';

class SubscriptionCubit extends Cubit<SubscriptionState> {
  SubscriptionCubit() : super(SubscriptionInitial());

  Future<void> verifySubscription(int? numAtendimento,) async {
    try {
      emit(LoadingVerifySubscriptionState());
      final response = await Locator.instance.get<GraphQlApi>().verifyExistenceSignature(attendanceNumber: numAtendimento);
      emit(SuccessSendVerifySubscriptionState(
        response,
      ));
    } catch (e) {
      emit(ErrorVerifySubscriptionState('$e'));
    }
  }
}
