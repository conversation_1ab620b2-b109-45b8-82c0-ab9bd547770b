import 'package:ambulancia_app/shared/api/attendance/attendance_api.dart';
import 'package:ambulancia_app/shared/api/attendance/delay_reason_api.dart';
import 'package:ambulancia_app/shared/api/attendance/motive.api.dart';
import 'package:ambulancia_app/shared/api/attendance/notification_of_amounts_of_data_pending_synchronization.api.dart';
import 'package:ambulancia_app/shared/api/attendance/signal_protocol_api.dart';
import 'package:ambulancia_app/shared/api/attendance/supplies.api.dart';
import 'package:ambulancia_app/shared/api/auth.api.dart';
import 'package:ambulancia_app/shared/api/clinic-evaluation.api.dart';
import 'package:ambulancia_app/shared/api/conduct.api.dart';
import 'package:ambulancia_app/shared/api/conduct/conduct_attachment.api.dart';
import 'package:ambulancia_app/shared/api/conduct/conduct_reclassification.api.dart';
import 'package:ambulancia_app/shared/api/graphql.api.dart';
import 'package:ambulancia_app/shared/api/photo.api.dart';
import 'package:ambulancia_app/shared/api/signature.api.dart';
import 'package:ambulancia_app/shared/api/vehicle.api.dart';
import 'package:ambulancia_app/shared/api/version.api.dart';
import 'package:ambulancia_app/shared/http-client.dart';
import 'package:ambulancia_app/shared/services/connectivity.service.dart';
import 'package:ambulancia_app/shared/services/geolocation.service.dart';
import 'package:ambulancia_app/shared/services/remote-config.service.dart';
import 'package:ambulancia_app/shared/services/sync-offline.service.dart';
import 'package:ambulancia_app/shared/services/version.service.dart';
import 'package:ambulancia_app/shared/utils/connectivity_utils.dart';
import 'package:ambulancia_app/shared/utils/offline-first/last-synchronization-dates/last_synchronization_dates.dart';
import 'package:get_it/get_it.dart';
import 'package:remote_log_elastic/remote_log_elastic.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'api/team.api.dart';
import 'api/websocket.api.dart';

class Locator {
  static late GetIt _i;
  static GetIt get instance => _i;

  static Future<void> initializeSharedPreferences() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();

    _i.registerSingleton<SharedPreferences>(prefs);
    _i.registerSingleton<LastSynchronizationDates>(
        LastSynchronizationDates(prefs));
  }

  static Future<void> registerRemoteConfigService() async {
    final connectivityResult = true;
    if (connectivityResult == true) {
      _i.registerSingleton<RemoteConfigService>(RemoteConfigService());
    } else {
      // CheckConnectionStream.stream.listen((result) async {
      //   if (result == true) {
      //     if (!_isRemoteConfigServiceRegistered) {
      //       _i.registerSingleton<RemoteConfigService>(RemoteConfigService());
      //       _isRemoteConfigServiceRegistered = true;
      //     }
      //   }
      // });
    }
  }

  Locator.setup() {
    _i = GetIt.I;
    _i.registerSingleton<RemoteLog>(
      RemoteLog(
          environment: (const String.fromEnvironment('remoteLogEnv')) == 'DEV'
              ? RemoteLogEnv.DEV
              : RemoteLogEnv.PROD,
          username: 'ambulancia-app',
          password: 'NwSWN5HRYcJnzsMex6QL',
          disableUpload: false,
          // disableUpload: FlavorConfig.instance.values.remoteLogEnv !=
          //     RemoteLogEnv.PROD, // Enable remote_log only production
          showLogs: false),
    );

    _i.registerSingleton<UnimedHttpClient>(
      UnimedHttpClient(defaultHeaders: {"Content-Type": "application/json"}),
    );

    _i.registerSingleton<VersionService>(VersionService());

    //TODO: Temporary disabled
    // _i.registerSingleton<OneSignalService>(OneSignalService());
    // _i.registerSingleton<NotificacaoOneSignal>(
    //     NotificacaoOneSignal(_i.get<OneSignalService>()));

    _i.registerLazySingleton<AuthApi>(
      () => AuthApi(_i.get<UnimedHttpClient>()),
    );

    _i.registerLazySingleton<VehicleApi>(
      () => VehicleApi(_i.get<UnimedHttpClient>()),
    );

    _i.registerLazySingleton<AttendanceApi>(
      () => AttendanceApi(_i.get<UnimedHttpClient>()),
    );

    _i.registerLazySingleton<SyncOfflineService>(
      () => SyncOfflineService(_i.get<UnimedHttpClient>()),
    );

    _i.registerLazySingleton<TeamApi>(
      () => TeamApi(_i.get<UnimedHttpClient>()),
    );

    _i.registerLazySingleton<GeolocationService>(() => GeolocationService());
    _i.registerLazySingleton<ConnectivityService>(
      () => ConnectivityService(),
    );
    _i.registerLazySingleton<ConnectivityUtils>(
      () =>
          ConnectivityUtils(connectivityService: _i.get<ConnectivityService>()),
    );
    _i.registerSingleton<ConductApi>(ConductApi(
      httpClient: _i.get<UnimedHttpClient>(),
    ));
    _i.registerSingleton<MotiveApi>(MotiveApi(
      _i.get<UnimedHttpClient>(),
    ));
    _i.registerSingleton<ClinicEvaluationApi>(ClinicEvaluationApi(
      httpClient: _i.get<UnimedHttpClient>(),
    ));
    _i.registerSingleton<SuppliesApi>(SuppliesApi(
      httpClient: _i.get<UnimedHttpClient>(),
    ));
    _i.registerSingleton<WebSocketApi>(WebSocketApi());
    _i.registerSingleton<SignatureApi>(SignatureApi(
      _i.get<UnimedHttpClient>(),
    ));

    _i.registerSingleton<ConductAttachmentAPI>(
        ConductAttachmentAPI(_i.get<UnimedHttpClient>()));

    _i.registerLazySingleton<
        NotificationOfAmountsOfDataPendingSynchronizationApi>(
      () => NotificationOfAmountsOfDataPendingSynchronizationApi(),
    );

    _i.registerLazySingleton<ConductReclassificationAPI>(
      () => ConductReclassificationAPI(_i.get<UnimedHttpClient>()),
    );

    _i.registerLazySingleton<DelayReasonApi>(
      () => DelayReasonApi(_i.get<UnimedHttpClient>()),
    );

    _i.registerLazySingleton<GraphQlApi>(
      () => GraphQlApi(_i.get<UnimedHttpClient>()),
    );

    _i.registerSingleton<PhotoOperatorApi>(
        PhotoOperatorApi(_i.get<UnimedHttpClient>()));
    _i.registerSingleton<VersionApi>(VersionApi(_i.get<UnimedHttpClient>()));
    _i.registerSingleton<SinalProtocolpi>(
        SinalProtocolpi(_i.get<UnimedHttpClient>()));
  }
}
