import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:ambulancia_app/shared/exceptions.dart';
import 'package:ambulancia_app/shared/http-client.dart';
import 'package:ambulancia_app/shared/messages.exceptions.dart';
import 'package:ambulancia_app/shared/utils/file.dart';
import 'package:ambulancia_app/shared/utils/http.dart';
import 'package:ambulancia_app/shared/utils/logger_print.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';

class SignatureApi {
  final UnimedHttpClient httpClient;
  final logger = UnimedLogger(className: 'SignatureApi');

  SignatureApi(this.httpClient);

  Future<String?> sendSignature(
      {required String numAtendimento,
      required String pathSignatureClient,
      required String nameClient,
      required String cpfOrRgOrCrmClient}) async {
    final url =
        '${const String.fromEnvironment('ambulanciaOsbUrl')}/unimedurgente/anexo/cliente';
    try {
      // HTTP
      final Map<String, dynamic> json = {
        "numAtendimento": numAtendimento,
        "codValidaAssinatura": cpfOrRgOrCrmClient,
        "nomeValidaAssinatura": nameClient,
      };
      final request = http.MultipartRequest('POST', Uri.parse(url));
      final fileHttp = await http.MultipartFile.fromPath(
          'arquivos', pathSignatureClient,
          filename: 'sign_${pathSignatureClient.split("/").last}');
      request.headers['Authorization'] =
          HttpUtils.getAuthorizationBasicAmbulanciaOsb();
      request.fields['obj'] = jsonEncode(json);
      request.files.add(fileHttp);
      final response = await request.send();
      final bodyResponse = await response.stream.bytesToString();

      if (response.statusCode == 200 || response.statusCode == 201) {
        final bodyResponseDecode = jsonDecode(bodyResponse);
        logger.i('sendSignature sucess - statusCode: ${response.statusCode}');
        return bodyResponseDecode['mensagem'];
      } else {
        logger.e('sendSignature error - statusCode: ${response.statusCode}');
        throw SignatureException(
            jsonDecode(bodyResponse)['mensagem'] ?? MessageException.general);
      }
    } on NoInternetException catch (e) {
      logger.e('sendSignaure error NoInternetException $e');
      throw NoInternetException();
    } on SocketException catch (e) {
      logger.e('sendSignaure error SocketException $e');
      throw NoInternetException();
    } on ServiceTimeoutException catch (e) {
      logger.e('sendSignaure error timeout $e');
      throw ServiceTimeoutException();
    } catch (e) {
      logger.e('sendSignature catch exeception: $e');
      throw SignatureException(MessageException.general);
    }
  }

  // Future<bool?> verifyExistenceSignature(
  //     int? numAtendimento, int? codStatus) async {
  //   bool? subscription;
  //   final endPoint = 'unimedurgente/anexos/atendimento/$numAtendimento';
  //   final url = '${const String.fromEnvironment('ambulanciaOsbUrl')}/$endPoint';

  //   try {
  //     final response = await httpClient.get(Uri.parse(url), headers: {
  //       'Authorization': HttpUtils.getAuthorizationBasicAmbulanciaOsb()
  //     });

  //     if (response.statusCode == 200) {
  //       final body = jsonDecode(response.body);
  //       logger.i(
  //           'verifyExistenceSignature sucess - statusCode: ${response.statusCode}');
  //       if (body.length > 0 && !(codStatus! < 5)) {
  //         body.forEach((anexo) => {
  //               if (anexo['nomeArquivo'].toString().startsWith('sign_'))
  //                 {subscription = true}
  //               else
  //                 debugPrint('Assinatura não existe')
  //             });
  //       } else
  //         subscription = false;
  //     } else {
  //       logger.e('sendSignature error - statusCode: ${response.statusCode}');
  //       throw VerifySubscriptionException(MessageException.signatureNoResponse);
  //     }
  //   } on NotFoundException catch (e) {
  //     logger.e('sendSignature NotFoundException: $e');
  //     NotFoundException();
  //   } on ServiceTimeoutException catch (e) {
  //     logger.e('sendSignature ServiceTimeoutException: $e');
  //     ServiceTimeoutException();
  //   } on NoInternetException catch (e) {
  //     logger.e('sendSignature NoInternetException: $e');
  //     NoInternetException();
  //   } catch (e) {
  //     logger.e('sendSignature error - statusCode: $e');
  //     throw VerifySubscriptionException(MessageException.signatureNoResponse);
  //   }

  //   return subscription;
  // }

  void saveImage({required base64String, serviceNumber}) async {
    Uint8List bytes = base64.decode(base64String);
    String dir = (await getApplicationDocumentsDirectory()).path;
    try {
      final oldFile = File('$dir/$serviceNumber.png');
      FileUtils.deleteFile(oldFile);
      imageCache.clear();
      final File newImage = File('$dir/$serviceNumber.png');
      newImage.writeAsBytes(bytes);
    } catch (e) {
      throw SaveSignatureInDocumentsException();
    }
  }

  Future<String> getFilePath(serviceNumber) async {
    Directory appDocumentsDirectory = await getApplicationDocumentsDirectory();
    try {
      String filePath = '${appDocumentsDirectory.path}/$serviceNumber.png';
      return filePath;
    } catch (e) {
      throw GetSignatureInDocumentsException();
    }
  }
}
